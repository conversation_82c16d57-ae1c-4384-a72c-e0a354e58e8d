import { useEffect, useRef, useCallback, useMemo, useState } from 'react';
import { Track, RoomEvent } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';

/**
 * Custom hook for managing noise suppression on audio tracks
 * @param {Object} room - LiveKit room instance
 * @param {boolean} noiseSuppressionEnabled - Whether noise suppression should be enabled
 * @param {function} setToastNotification - Function to show toast notifications
 * @param {function} setToastStatus - Function to set toast status
 * @param {function} setShowToast - Function to show/hide toast
 * @returns {Object} - Hook state and methods
 */
export const useNoiseSuppression = (
  room,
  noiseSuppressionEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast
) => {
  // Refs for managing noise suppression state
  const noiseProcessorRef = useRef(null);
  const originalTrackRef = useRef(null);
  const isProcessingRef = useRef(false);
  const processedTrackRef = useRef(null);
  const isNoiseSuppressionActiveRef = useRef(false);

  // State to track audio track publication changes
  const [audioTrackState, setAudioTrackState] = useState({
    isPublished: false,
    trackId: null
  });

  // Memoize toast functions to prevent unnecessary re-renders
  const showToast = useCallback((message, status) => {
    if (setToastNotification && setToastStatus && setShowToast) {
      setToastNotification(message);
      setToastStatus(status);
      setShowToast(true);
    }
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Memoize audio constraints to prevent recreation
  const audioConstraints = useMemo(() => ({
    audio: {
      echoCancellation: true,
      noiseSuppression: false,
      autoGainControl: true
    }
  }), []);

  // Cleanup function for noise suppression
  const cleanupNoiseSuppression = useCallback(async () => {
    if (noiseProcessorRef.current) {
      try {
        await noiseProcessorRef.current.stopProcessing();
      } catch (error) {
        // Silently handle cleanup errors
      }
      noiseProcessorRef.current = null;
    }

    if (originalTrackRef.current) {
      originalTrackRef.current.stop();
      originalTrackRef.current = null;
    }

    processedTrackRef.current = null;
    isNoiseSuppressionActiveRef.current = false;
  }, []);

  // Enable noise suppression
  const enableNoiseSuppression = useCallback(async (localAudioTrack) => {
    // Store the original track before processing (clone it to preserve)
    if (!originalTrackRef.current) {
      originalTrackRef.current = localAudioTrack.mediaStreamTrack.clone();
    }

    // Create noise suppression processor
    noiseProcessorRef.current = new NoiseSuppressionProcessor();

    // Start processing the audio track
    const processedTrack = await noiseProcessorRef.current.startProcessing(
      localAudioTrack.mediaStreamTrack
    );

    if (processedTrack) {
      // Store the processed track reference
      processedTrackRef.current = processedTrack;

      // Replace the original track with the processed one
      await localAudioTrack.replaceTrack(processedTrack, true);

      // Mark noise suppression as active
      isNoiseSuppressionActiveRef.current = true;

      showToast("Noise suppression enabled", "success");
      return true;
    }
    return false;
  }, [showToast]);

  // Disable noise suppression
  const disableNoiseSuppression = useCallback(async (localAudioTrack) => {
    // Stop the noise processor first
    if (noiseProcessorRef.current) {
      await noiseProcessorRef.current.stopProcessing();
      noiseProcessorRef.current = null;
    }
    processedTrackRef.current = null;

    // Create a fresh audio track to replace the processed one
    const currentSettings = localAudioTrack.mediaStreamTrack.getSettings();
    const constraints = {
      ...audioConstraints,
      audio: {
        ...audioConstraints.audio,
        deviceId: currentSettings.deviceId || 'default'
      }
    };

    const stream = await navigator.mediaDevices.getUserMedia(constraints);
    const newAudioTrack = stream.getAudioTracks()[0];

    if (newAudioTrack) {
      await localAudioTrack.replaceTrack(newAudioTrack, true);
    }

    // Clear references
    if (originalTrackRef.current) {
      originalTrackRef.current.stop();
      originalTrackRef.current = null;
    }

    // Mark noise suppression as inactive
    isNoiseSuppressionActiveRef.current = false;

    showToast("Noise suppression disabled", "info");
    return true;
  }, [audioConstraints, showToast]);

  // Effect to monitor audio track publication changes
  useEffect(() => {
    if (!room || room.state !== "connected") {
      setAudioTrackState({ isPublished: false, trackId: null });
      return;
    }

    const updateAudioTrackState = () => {
      const audioTrackPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
      const isPublished = !!(audioTrackPublication && audioTrackPublication.track);
      const trackId = audioTrackPublication?.track?.sid || null;

      setAudioTrackState(prev => {
        // Only update if state actually changed
        if (prev.isPublished !== isPublished || prev.trackId !== trackId) {
          return { isPublished, trackId };
        }
        return prev;
      });
    };

    // Initial check
    updateAudioTrackState();

    // Listen for track published/unpublished events
    const handleTrackPublished = (publication) => {
      if (publication.source === Track.Source.Microphone) {
        updateAudioTrackState();
      }
    };

    const handleTrackUnpublished = (publication) => {
      if (publication.source === Track.Source.Microphone) {
        updateAudioTrackState();
      }
    };

    room.localParticipant.on(RoomEvent.LocalTrackPublished, handleTrackPublished);
    room.localParticipant.on(RoomEvent.LocalTrackUnpublished, handleTrackUnpublished);

    return () => {
      room.localParticipant.off(RoomEvent.LocalTrackPublished, handleTrackPublished);
      room.localParticipant.off(RoomEvent.LocalTrackUnpublished, handleTrackUnpublished);
    };
  }, [room, room?.state]);

  useEffect(() => {
    const applyNoiseSuppression = async () => {
      if (!room || room.state !== "connected") return;

      // Prevent multiple simultaneous processing attempts
      if (isProcessingRef.current) {
        return;
      }

      // Check if we're already in the desired state
      if (noiseSuppressionEnabled === isNoiseSuppressionActiveRef.current) {
        return;
      }

      // Only proceed if audio track is published
      if (!audioTrackState.isPublished) {
        return;
      }

      try {
        isProcessingRef.current = true;

        // Get the local audio track publication
        const audioTrackPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
        if (!audioTrackPublication || !audioTrackPublication.track) {
          return;
        }

        const localAudioTrack = audioTrackPublication.track;

        if (noiseSuppressionEnabled && !isNoiseSuppressionActiveRef.current) {
          await enableNoiseSuppression(localAudioTrack);
        } else if (!noiseSuppressionEnabled && isNoiseSuppressionActiveRef.current) {
          try {
            await disableNoiseSuppression(localAudioTrack);
          } catch (restoreError) {
            showToast("Failed to restore audio", "error");
          }
        }
      } catch (error) {
        showToast("Failed to update noise suppression", "error");
      } finally {
        isProcessingRef.current = false;
      }
    };

    // Run when room is connected, audio track state changes, or noise suppression setting changes
    if (room && room.state === "connected") {
      // Small delay to ensure audio track is fully initialized
      const timeoutId = setTimeout(applyNoiseSuppression, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [room, room?.state, noiseSuppressionEnabled, audioTrackState, enableNoiseSuppression, disableNoiseSuppression, showToast]);

  // Cleanup effect when audio track is unpublished
  useEffect(() => {
    if (!audioTrackState.isPublished && isNoiseSuppressionActiveRef.current) {
      // Audio track was unpublished, cleanup noise suppression
      cleanupNoiseSuppression();
    }
  }, [audioTrackState.isPublished, cleanupNoiseSuppression]);

  // Separate cleanup effect for when component unmounts
  useEffect(() => {
    return () => {
      cleanupNoiseSuppression();
      isProcessingRef.current = false;
    };
  }, [cleanupNoiseSuppression]);

  // Return hook state and methods
  return {
    isNoiseSuppressionActive: isNoiseSuppressionActiveRef.current,
    isProcessing: isProcessingRef.current,
  };
};
